#!/usr/bin/env python3
"""
服务器配置诊断脚本
"""
import os
import sys

print("=== 服务器配置诊断 ===")
print("当前工作目录: {}".format(os.getcwd()))
print("Python版本: {}".format(sys.version))

# 检查.env文件
env_files = ['.env', 'backend/.env', '../.env']
for env_file in env_files:
    if os.path.exists(env_file):
        print("找到.env文件: {}".format(env_file))
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines, 1):
                if 'FLASK_PORT' in line:
                    print("  第{}行: {}".format(i, line.strip()))
        break
else:
    print("❌ 未找到.env文件")

# 检查环境变量（加载前）
print("\n=== 系统环境变量 ===")
print("FLASK_PORT: {}".format(os.environ.get('FLASK_PORT', '未设置')))
print("FLASK_HOST: {}".format(os.environ.get('FLASK_HOST', '未设置')))
print("FLASK_DEBUG: {}".format(os.environ.get('FLASK_DEBUG', '未设置')))

# 尝试加载dotenv
print("\n=== 加载dotenv ===")
try:
    from dotenv import load_dotenv
    
    # 尝试不同的.env文件路径
    for env_file in ['.env', 'backend/.env', '../.env']:
        if os.path.exists(env_file):
            print("尝试加载: {}".format(env_file))
            result = load_dotenv(env_file)
            print("load_dotenv({}) 返回: {}".format(env_file, result))
            break
    else:
        # 默认加载
        result = load_dotenv()
        print("load_dotenv() 返回: {}".format(result))
    
    print("\n=== 加载后的环境变量 ===")
    print("FLASK_PORT: {}".format(os.getenv('FLASK_PORT', '未设置')))
    print("FLASK_HOST: {}".format(os.getenv('FLASK_HOST', '未设置')))
    print("FLASK_DEBUG: {}".format(os.getenv('FLASK_DEBUG', '未设置')))
    
except Exception as e:
    print("❌ dotenv加载失败: {}".format(e))

# 测试配置类
print("\n=== 配置类测试 ===")
try:
    # 添加当前目录到Python路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    from config import get_config
    config = get_config()
    print("配置类加载成功")
    print("config.FLASK_HOST: {}".format(config.FLASK_HOST))
    print("config.FLASK_PORT: {}".format(config.FLASK_PORT))
    print("config.FLASK_DEBUG: {}".format(config.FLASK_DEBUG))
    
    # 检查配置类中的原始环境变量读取
    print("\n=== 直接读取环境变量 ===")
    print("os.getenv('FLASK_PORT', '5000'): {}".format(os.getenv('FLASK_PORT', '5000')))
    print("int(os.getenv('FLASK_PORT', '5000')): {}".format(int(os.getenv('FLASK_PORT', '5000'))))
    
except Exception as e:
    print("❌ 配置类测试失败: {}".format(e))
    import traceback
    traceback.print_exc()

print("\n=== 诊断完成 ===")

#!/usr/bin/env python3
"""
深度诊断配置问题
"""
import os
import sys

print("=== 深度配置诊断 ===")

# 1. 检查.env文件内容
print("\n1. 检查.env文件内容:")
with open('.env', 'r', encoding='utf-8') as f:
    for i, line in enumerate(f, 1):
        if 'FLASK' in line:
            print("  第{}行: {}".format(i, line.strip()))

# 2. 加载dotenv前的环境变量
print("\n2. 加载dotenv前的环境变量:")
print("  FLASK_PORT: {}".format(os.environ.get('FLASK_PORT', '未设置')))

# 3. 加载dotenv
print("\n3. 加载dotenv:")
from dotenv import load_dotenv
result = load_dotenv()
print("  load_dotenv() 返回: {}".format(result))

# 4. 加载dotenv后的环境变量
print("\n4. 加载dotenv后的环境变量:")
print("  FLASK_PORT: {}".format(os.environ.get('FLASK_PORT', '未设置')))
print("  os.getenv('FLASK_PORT'): {}".format(os.getenv('FLASK_PORT')))
print("  os.getenv('FLASK_PORT', '5000'): {}".format(os.getenv('FLASK_PORT', '5000')))

# 5. 测试配置类的每个步骤
print("\n5. 配置类逐步测试:")

# 导入配置模块
from config.settings import Config

print("  Config.FLASK_PORT (类属性): {}".format(Config.FLASK_PORT))

# 测试get_config函数
from config import get_config
config = get_config()
print("  config实例类型: {}".format(type(config)))
print("  config.FLASK_PORT: {}".format(config.FLASK_PORT))

# 6. 直接测试环境变量读取
print("\n6. 直接测试环境变量读取:")
direct_port = int(os.getenv('FLASK_PORT', '5000'))
print("  int(os.getenv('FLASK_PORT', '5000')): {}".format(direct_port))

# 7. 检查配置类定义
print("\n7. 检查配置类定义:")
print("  Config类的FLASK_PORT定义:")
import inspect
source_lines = inspect.getsourcelines(Config)
for i, line in enumerate(source_lines[0]):
    if 'FLASK_PORT' in line:
        print("    第{}行: {}".format(source_lines[1] + i, line.strip()))

# 8. 测试不同的配置类
print("\n8. 测试不同的配置类:")
from config.settings import DevelopmentConfig, ProductionConfig, DockerConfig

dev_config = DevelopmentConfig()
print("  DevelopmentConfig.FLASK_PORT: {}".format(dev_config.FLASK_PORT))

prod_config = ProductionConfig()
print("  ProductionConfig.FLASK_PORT: {}".format(prod_config.FLASK_PORT))

docker_config = DockerConfig()
print("  DockerConfig.FLASK_PORT: {}".format(docker_config.FLASK_PORT))

print("\n=== 诊断完成 ===")

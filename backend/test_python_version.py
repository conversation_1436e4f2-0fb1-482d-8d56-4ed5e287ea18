#!/usr/bin/env python3
"""
测试Python版本兼容性
"""
import sys
import os

print("=== Python版本信息 ===")
print(f"Python版本: {sys.version}")
print(f"Python版本信息: {sys.version_info}")
print(f"Python可执行文件: {sys.executable}")

# 检查Python版本
if sys.version_info < (3, 6):
    print("❌ 警告: Python版本过低，建议使用Python 3.6+")
else:
    print("✅ Python版本符合要求")

# 测试基本的环境变量加载
print("\n=== 测试基本功能 ===")

try:
    # 测试dotenv
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ dotenv加载成功")
    
    port = os.getenv('FLASK_PORT', '5000')
    print(f"✅ 读取FLASK_PORT: {port}")
    
    # 测试类型转换
    port_int = int(port)
    print(f"✅ 端口转换为整数: {port_int}")
    
except Exception as e:
    print(f"❌ 基本功能测试失败: {e}")
    import traceback
    traceback.print_exc()

# 测试Flask配置
print("\n=== 测试Flask配置 ===")
try:
    from config import get_config
    config = get_config()
    print(f"✅ 配置加载成功")
    print(f"   FLASK_HOST: {config.FLASK_HOST}")
    print(f"   FLASK_PORT: {config.FLASK_PORT} (类型: {type(config.FLASK_PORT)})")
    print(f"   FLASK_DEBUG: {config.FLASK_DEBUG} (类型: {type(config.FLASK_DEBUG)})")
    
except Exception as e:
    print(f"❌ Flask配置测试失败: {e}")
    import traceback
    traceback.print_exc()

# 测试简单的Flask应用
print("\n=== 测试Flask应用创建 ===")
try:
    from flask import Flask
    test_app = Flask(__name__)
    print("✅ Flask应用创建成功")
    
    # 测试配置设置
    from config import get_config
    config = get_config()
    
    print(f"准备使用配置:")
    print(f"  host={config.FLASK_HOST}")
    print(f"  port={config.FLASK_PORT}")
    print(f"  debug={config.FLASK_DEBUG}")
    
except Exception as e:
    print(f"❌ Flask应用测试失败: {e}")
    import traceback
    traceback.print_exc()

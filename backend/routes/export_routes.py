"""
导出相关路由
"""
from flask import Blueprint, request, send_file
from datetime import datetime
from services import ExportService
from backend.old.utils import success_response, error_response

export_bp = Blueprint('export', __name__)

# 全局变量，在app.py中初始化
export_service = None


def init_export_routes(export_svc):
    """初始化路由依赖"""
    global export_service
    export_service = export_svc


@export_bp.route('/export_results/<task_id>')
def export_results(task_id):
    """导出分析结果为Excel"""
    try:
        if not export_service.has_pandas:
            return error_response('pandas未安装，无法导出Excel文件')
        
        filepath = export_service.export_analysis_results(task_id)
        
        if not filepath:
            return error_response('没有找到分析结果')
        
        filename = f"analysis_results_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        return send_file(filepath, as_attachment=True, download_name=filename)
        
    except Exception as e:
        return error_response(f'导出失败: {str(e)}')


@export_bp.route('/export_filtered_results', methods=['POST'])
def export_filtered_results():
    """导出筛选后的结果"""
    try:
        data = request.json
        results_data = data.get('data', [])
        filters_info = data.get('filters', {})
        export_format = data.get('format', 'excel')
        
        if not results_data:
            return error_response('没有数据可导出')
        
        if not export_service.has_pandas:
            return error_response('pandas未安装，无法导出文件')
        
        print(f"📊 导出筛选结果: {len(results_data)} 条记录")
        
        # 导出数据
        output = export_service.export_filtered_results(results_data, filters_info, export_format)
        
        # 生成文件名
        filename = export_service.generate_export_filename(filters_info, export_format)
        
        # 获取MIME类型
        mimetype = export_service.get_export_mimetype(export_format)
        
        print(f"✅ 筛选结果导出成功: {filename}")
        
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )
        
    except Exception as e:
        print(f"❌ 导出筛选结果失败: {e}")
        return error_response(f'导出失败: {str(e)}')

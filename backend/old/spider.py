"""
    downloads:
    《年度报告》 2024年
"""
import requests
import random
import time
import urllib
import json

download_path = 'http://static.cninfo.com.cn/'
saving_path = './pdf/'
orgid_url = 'http://www.cninfo.com.cn/new/data/szse_stock.json'

User_Agent = [
    "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Win64; x64; Trident/5.0; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 2.0.50727; Media Center PC 6.0)",
    "Mozilla/5.0 (compatible; MSIE 8.0; Windows NT 6.0; Trident/4.0; WOW64; Trident/4.0; SLCC2; .NET CLR 2.0.50727; .NET CLR 3.5.30729; .NET CLR 3.0.30729; .NET CLR 1.0.3705; .NET CLR 1.1.4322)",
    "Mozilla/4.0 (compatible; MSIE 7.0b; Windows NT 5.2; .NET CLR 1.1.4322; .NET CLR 2.0.50727; InfoPath.2; .NET CLR 3.0.04506.30)",
    "Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN) AppleWebKit/523.15 (KHTML, like Gecko, Safari/419.3) Arora/0.3 (Change: 287 c9dfb30)",
    "Mozilla/5.0 (X11; U; Linux; en-US) AppleWebKit/527+ (KHTML, like Gecko, Safari/419.3) Arora/0.6",
    "Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.8.1.2pre) Gecko/20070215 K-Ninja/2.1.1",
    "Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9) Gecko/20080705 Firefox/3.0 Kapiko/3.0"
]


headers = {'Accept': 'application/json, text/javascript, */*; q=0.01',
           "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
           "Accept-Encoding": "gzip, deflate",
           "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-HK;q=0.6,zh-TW;q=0.5",
           'Host': 'www.cninfo.com.cn',
           'Origin': 'http://www.cninfo.com.cn',
           'Referer': 'http://www.cninfo.com.cn/new/commonUrl?url=disclosure/list/notice',
           'X-Requested-With': 'XMLHttpRequest'
           }


def get_orgid_by_code(stock_code):
    """根据股票代码获取orgId"""
    try:
        response = requests.get(orgid_url, headers=headers)
        if response.status_code == 200:
            data = response.json()
            stock_lists = data.get('stockList', [])
            for stock_info in stock_lists:
                if stock_info.get('code') == stock_code:
                    return {
                        'code': stock_info['code'],
                        'orgId': stock_info['orgId'],
                        'zwjc': stock_info.get('zwjc', ''),
                    }
        print(f"未找到股票代码 {stock_code} 的orgId")
        return None
    except Exception as e:
        print(f"获取orgId失败: {e}")
        return None




# 深市 年度报告
def szseAnnual(page, stock_code, orgId):
    query_path = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    headers['User-Agent'] = random.choice(User_Agent)  # 定义User_Agent
    query = {'pageNum': page,  # 页码
             'pageSize': 30,
             'tabName': 'fulltext',
             'column': 'szse',  # 深交所
             'stock': f'{stock_code},{orgId}',  # 使用新格式
             'searchkey': '',
             'secid': '',
             'plate': 'sz',
             'category': 'category_ndbg_szsh',  # 年度报告，去掉分号
             'trade': '',
             'seDate': '2024-01-01~2025-12-31',  # 2024年年报时间范围
             'sortName': '',
             'sortType': '',
             'isHLtitle': 'true'
             }

    try:
        namelist = requests.post(query_path, headers=headers, data=query)
        result = namelist.json()
        return result.get('announcements', [])
    except Exception as e:
        print(f"深市年报查询失败: {e}")
        return []


# 沪市 年度报告
def sseAnnual(page, stock_code, orgId):
    query_path = 'http://www.cninfo.com.cn/new/hisAnnouncement/query'
    headers['User-Agent'] = random.choice(User_Agent)  # 定义User_Agent
    query = {'pageNum': page,  # 页码
             'pageSize': 30,
             'tabName': 'fulltext',
             'column': 'sse',
             'stock': f'{stock_code},{orgId}',  # 使用新格式
             'searchkey': '',
             'secid': '',
             'plate': 'sh',
             'category': 'category_ndbg_szsh',  # 年度报告，去掉分号
             'trade': '',
             'seDate': '2024-01-01~2025-12-31',  # 2024年年报时间范围
             'sortName': '',
             'sortType': '',
             'isHLtitle': 'true'
             }

    try:
        namelist = requests.post(query_path, headers=headers, data=query)
        result = namelist.json()
        return result.get('announcements', [])
    except Exception as e:
        print(f"沪市年报查询失败: {e}")
        return []





# download PDF
def Download(single_page):
    if single_page is None:
        return

    headers = {'Accept': 'application/json, text/javascript, */*; q=0.01',
               "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
               "Accept-Encoding": "gzip, deflate",
               "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-HK;q=0.6,zh-TW;q=0.5",
               'Host': 'www.cninfo.com.cn',
               'Origin': 'http://www.cninfo.com.cn'
               }

    for i in single_page:
        title = i['announcementTitle']

        # 排除摘要和确认意见
        if '确认意见' in title or '摘要' in title:
            print(f"跳过摘要文件: {title}")
            continue

        # 检查是否是2024年年度报告（不是摘要）
        if '2024年年度报告' in title and '摘要' not in title:
            print(f"准备下载: {title}")
            allowed = True
        elif '年度报告' in title and '摘要' not in title:
            print(f"发现其他年份年报: {title}")
            allowed = False  # 只要2024年的
        else:
            allowed = False

        if allowed:
            download = download_path + i["adjunctUrl"]
            name = i["secCode"] + '_' + i['secName'] + '_' + i['announcementTitle'] + '.pdf'
            if '*' in name:
                name = name.replace('*', '')
            file_path = saving_path + name
            time.sleep(random.random() * 2)

            headers['User-Agent'] = random.choice(User_Agent)
            try:
                r = requests.get(download, timeout=30)
                if r.status_code == 200:
                    with open(file_path, "wb") as f:
                        f.write(r.content)
                    print(f"✅ 下载成功: {name}")
                else:
                    print(f"❌ 下载失败: {name} (状态码: {r.status_code})")
            except Exception as e:
                print(f"❌ 下载异常: {name} (错误: {e})")
        else:
            continue


# given page_number & stock number
def Run(page_number, stock_code):
    # 首先获取orgId
    stock_info = get_orgid_by_code(stock_code.strip())
    if not stock_info:
        print(f"跳过股票代码 {stock_code}：无法获取orgId")
        return

    orgId = stock_info['orgId']
    company_name = stock_info['zwjc']
    print(f"处理股票: {stock_code} ({company_name})")

    try:
        annual_report = szseAnnual(page_number, stock_code, orgId)
        annual_report_ = sseAnnual(page_number, stock_code, orgId)
    except Exception as e:
        print(f"{stock_code} 查询失败: {e}")
        return

    # 合并结果
    all_reports = []
    if annual_report:
        all_reports.extend(annual_report)
    if annual_report_:
        all_reports.extend(annual_report_)

    if all_reports:
        print(f"找到 {len(all_reports)} 条公告")
        Download(all_reports)
    else:
        print(f"{stock_code} 没有找到相关公告")


with open('company_id.txt') as file:
    lines = file.readlines()
    total_stocks = len([line.strip() for line in lines if line.strip()])

    print(f"开始爬取 {total_stocks} 个股票的年报...")

    for i, line in enumerate(lines, 1):
        stock_code = line.strip()
        if stock_code:  # 跳过空行
            print(f"\n[{i}/{total_stocks}] 处理股票: {stock_code}")
            Run(1, stock_code)
            print(f"{stock_code} 完成")
            time.sleep(random.uniform(1, 3))  # 添加随机延时

    print(f"\n🎉 所有股票处理完成！共处理 {total_stocks} 个股票")

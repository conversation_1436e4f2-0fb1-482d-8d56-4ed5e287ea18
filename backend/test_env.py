#!/usr/bin/env python3
"""
测试环境变量加载
"""
import os
import sys

print("=== 环境变量测试 ===")
print(f"当前工作目录: {os.getcwd()}")
print(f"Python路径: {sys.executable}")

# 检查.env文件是否存在
env_file = '.env'
if os.path.exists(env_file):
    print(f"✅ .env文件存在: {env_file}")
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print("📄 .env文件内容:")
        for i, line in enumerate(content.split('\n'), 1):
            if 'FLASK_PORT' in line:
                print(f"  {i}: {line} ⭐")
            elif line.strip() and not line.startswith('#'):
                print(f"  {i}: {line}")
else:
    print(f"❌ .env文件不存在: {env_file}")

print("\n=== 加载前的环境变量 ===")
print(f"FLASK_PORT (系统): {os.getenv('FLASK_PORT', '未设置')}")
print(f"FLASK_HOST (系统): {os.getenv('FLASK_HOST', '未设置')}")
print(f"FLASK_DEBUG (系统): {os.getenv('FLASK_DEBUG', '未设置')}")

# 尝试加载.env文件
try:
    from dotenv import load_dotenv
    print("\n=== 加载dotenv ===")
    result = load_dotenv()
    print(f"load_dotenv() 返回: {result}")
    
    print("\n=== 加载后的环境变量 ===")
    print(f"FLASK_PORT: {os.getenv('FLASK_PORT', '未设置')}")
    print(f"FLASK_HOST: {os.getenv('FLASK_HOST', '未设置')}")
    print(f"FLASK_DEBUG: {os.getenv('FLASK_DEBUG', '未设置')}")
    
except ImportError:
    print("❌ python-dotenv 未安装")
except Exception as e:
    print(f"❌ 加载dotenv失败: {e}")

# 测试配置类
try:
    print("\n=== 测试配置类 ===")
    from config import get_config
    config = get_config()
    print(f"config.FLASK_HOST: {config.FLASK_HOST}")
    print(f"config.FLASK_PORT: {config.FLASK_PORT}")
    print(f"config.FLASK_DEBUG: {config.FLASK_DEBUG}")
except Exception as e:
    print(f"❌ 配置类测试失败: {e}")
    import traceback
    traceback.print_exc()

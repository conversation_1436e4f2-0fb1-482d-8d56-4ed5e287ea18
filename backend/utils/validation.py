"""
验证工具模块
"""


def validate_required_fields(data, required_fields):
    """验证必需字段"""
    if not data:
        return "请求数据不能为空"
    
    missing_fields = []
    for field in required_fields:
        if field not in data or not data[field]:
            missing_fields.append(field)
    
    if missing_fields:
        return f"缺少必需字段: {', '.join(missing_fields)}"
    
    return None


def validate_stock_codes(stock_codes_str: str) -> Optional[str]:
    """验证股票代码格式"""
    if not stock_codes_str or not stock_codes_str.strip():
        return "请输入股票代码"
    
    stock_codes = [code.strip() for code in stock_codes_str.split('\n') if code.strip()]
    
    if not stock_codes:
        return "请输入有效的股票代码"
    
    # 简单的股票代码格式验证
    for code in stock_codes:
        if not code.isdigit() or len(code) != 6:
            return f"股票代码格式错误: {code}，应为6位数字"
    
    return None


def validate_keywords(keywords_str: str) -> Optional[str]:
    """验证关键词"""
    if not keywords_str or not keywords_str.strip():
        return "请输入关键词"
    
    keywords = [kw.strip() for kw in keywords_str.split('\n') if kw.strip()]
    
    if not keywords:
        return "请输入有效的关键词"
    
    # 检查关键词长度
    for keyword in keywords:
        if len(keyword) > 50:
            return f"关键词过长: {keyword}，最多50个字符"
    
    return None


def validate_date_range(start_date: str, end_date: str) -> Optional[str]:
    """验证日期范围"""
    try:
        from datetime import datetime
        
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        
        if start > end:
            return "开始日期不能晚于结束日期"
        
        # 检查日期范围是否合理（不超过10年）
        if (end - start).days > 3650:
            return "日期范围不能超过10年"
        
        return None
        
    except ValueError:
        return "日期格式错误，应为YYYY-MM-DD格式"


def validate_pagination(page: Any, page_size: Any) -> tuple:
    """验证分页参数"""
    try:
        page = int(page) if page else 1
        page_size = int(page_size) if page_size else 20
        
        if page < 1:
            page = 1
        
        if page_size < 1:
            page_size = 20
        elif page_size > 100:
            page_size = 100
        
        return page, page_size
        
    except (ValueError, TypeError):
        return 1, 20

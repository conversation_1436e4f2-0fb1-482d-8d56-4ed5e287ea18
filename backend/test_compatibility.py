#!/usr/bin/env python
"""
测试Python3兼容性
"""
import sys
import os

print("=== Python兼容性测试 ===")
print("Python版本: {}".format(sys.version))

# 测试环境变量加载
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ dotenv加载成功")
except Exception as e:
    print("❌ dotenv加载失败: {}".format(e))

# 测试配置加载
try:
    from config import get_config
    config = get_config()
    print("✅ 配置加载成功")
    print("   Flask主机: {}".format(config.FLASK_HOST))
    print("   Flask端口: {}".format(config.FLASK_PORT))
    print("   Flask调试: {}".format(config.FLASK_DEBUG))
except Exception as e:
    print("❌ 配置加载失败: {}".format(e))
    import traceback
    traceback.print_exc()

# 测试工具函数
try:
    from utils import success_response, error_response, validate_required_fields
    print("✅ 工具函数导入成功")
    
    # 测试响应函数
    resp = success_response({"test": "data"}, "测试消息")
    print("✅ success_response测试通过")
    
    # 测试验证函数
    result = validate_required_fields({"field1": "value1"}, ["field1"])
    if result is None:
        print("✅ validate_required_fields测试通过")
    else:
        print("❌ validate_required_fields测试失败: {}".format(result))
        
except Exception as e:
    print("❌ 工具函数测试失败: {}".format(e))
    import traceback
    traceback.print_exc()

print("\n=== 测试完成 ===")
print("如果所有测试都通过，说明代码已兼容Python3")

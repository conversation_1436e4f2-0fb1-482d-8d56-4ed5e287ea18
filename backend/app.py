"""
Flask Web应用主文件 - 重构版本
"""
import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, request
from flask_cors import CORS

# 导入配置
from config import get_config

# 导入模型和服务
from models import DatabaseManager, TaskManager
from services import SpiderService, AnalysisService, AIService, ExportService

# 导入路由
from routes import register_routes
from routes.analysis_routes import init_analysis_routes
from routes.data_routes import init_data_routes
from routes.ai_routes import init_ai_routes
from routes.export_routes import init_export_routes

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 成功加载.env文件")
except ImportError:
    print("⚠️ python-dotenv未安装，将直接使用系统环境变量")
except Exception as e:
    print(f"⚠️ 加载.env文件失败: {e}")

# 检查依赖
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    print("警告: pandas未安装，Excel导出功能将受限")

try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False
    print("警告: openai未安装，AI分析功能将受限")


def create_app():
    """创建Flask应用"""
    # 获取配置
    config = get_config()
    
    # 创建Flask应用
    app = Flask(__name__)
    app.config.from_object(config)
    
    # 启用CORS - 允许局域网访问
    CORS(app)  # 最简单的CORS配置，允许所有来源

    # 添加请求日志中间件
    @app.before_request
    def log_request_info():
        print(f"📱 收到请求: {request.method} {request.url}")
        print(f"   来源: {request.headers.get('Origin', 'Unknown')}")

    @app.after_request
    def after_request(response):
        print(f"📤 响应状态: {response.status_code}")
        # 手动添加CORS头部（双重保险）
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type,Authorization'
        response.headers['Access-Control-Allow-Methods'] = 'GET,POST,PUT,DELETE,OPTIONS'
        return response
    
    # 打印配置信息
    config.print_config()
    
    # 确保必要目录存在
    config.ensure_directories()
    
    # 初始化数据库
    db_path = config.get_database_path()
    db_manager = DatabaseManager(db_path=db_path)
    
    # 初始化任务管理器
    task_manager = TaskManager()
    
    # 初始化服务
    spider_service = SpiderService(db_manager)
    analysis_service = AnalysisService(db_manager)
    ai_service = AIService(db_manager)
    export_service = ExportService(db_manager)
    
    # 初始化路由依赖
    init_analysis_routes(spider_service, analysis_service, task_manager)
    init_data_routes(spider_service, analysis_service)
    init_ai_routes(ai_service)
    init_export_routes(export_service)
    
    # 注册路由
    register_routes(app)

    # 自动导入TXT文件（Docker环境）
    if os.path.exists('/.dockerenv'):
        auto_import_txt_files(db_manager)

    return app


def auto_import_txt_files(db_manager):
    """容器启动时自动导入TXT文件"""
    try:
        txt_dir = 'txt'
        if not os.path.exists(txt_dir):
            print("📁 TXT目录不存在，跳过自动导入")
            return
        
        # 检查数据库中是否已有数据
        existing_reports = db_manager.get_all_reports()
        if existing_reports:
            print(f"📊 数据库中已有 {len(existing_reports)} 条年报记录，跳过自动导入")
            return
        
        # 获取TXT文件列表
        txt_files = [f for f in os.listdir(txt_dir) if f.endswith('.txt')]
        if not txt_files:
            print("📁 TXT目录为空，跳过自动导入")
            return
        
        print(f"🚀 检测到 {len(txt_files)} 个TXT文件，开始自动导入...")
        
        imported = 0
        skipped = 0
        errors = 0
        
        for txt_file in txt_files:
            try:
                file_path = os.path.join(txt_dir, txt_file)
                
                # 解析文件名获取信息
                parts = txt_file.replace('.txt', '').split('_')
                if len(parts) >= 3:
                    stock_code = parts[0]
                    company_name = parts[1]
                    report_title = '_'.join(parts[2:])
                else:
                    stock_code = 'Unknown'
                    company_name = 'Unknown'
                    report_title = txt_file.replace('.txt', '')
                
                # 检查是否已存在
                if db_manager.report_exists(stock_code, txt_file):
                    skipped += 1
                    continue
                
                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if content.strip():
                    # 添加到数据库
                    db_manager.add_report(
                        stock_code=stock_code,
                        company_name=company_name,
                        report_title=report_title,
                        file_name=txt_file,
                        file_path=file_path,
                        txt_content=content
                    )
                    imported += 1
                    
                    # 添加公司信息（如果不存在）
                    try:
                        db_manager.add_company(stock_code, company_name, '')
                    except:
                        pass  # 公司可能已存在
                else:
                    errors += 1
                    
            except Exception as e:
                print(f"❌ 导入文件失败 {txt_file}: {e}")
                errors += 1
        
        print(f"✅ 自动导入完成: 新增 {imported} 个，跳过 {skipped} 个，失败 {errors} 个")
        
    except Exception as e:
        print(f"❌ 自动导入过程出错: {e}")


if __name__ == '__main__':
    app = create_app()

    # 获取配置
    config = get_config()

    # 开发环境运行
    print("🚀 启动Flask开发服务器...")
    print("   主机: {}".format(config.FLASK_HOST))
    print("   端口: {}".format(config.FLASK_PORT))
    print("   调试模式: {}".format(config.FLASK_DEBUG))
    app.run(
        host=config.FLASK_HOST,
        port=config.FLASK_PORT,
        debug=config.FLASK_DEBUG
    )

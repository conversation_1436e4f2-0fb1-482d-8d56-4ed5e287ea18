# CNInfo 年报分析系统

基于 [cninfo_process](https://github.com/gaodechen/cninfo_process) 开发的年报爬虫和分析工具，支持Web界面和AI分析功能。

## 项目结构

```
cninfo_process/
├── backend/          # Flask后端服务
├── frontend/         # React前端界面
├── docker-deploy/    # Docker部署配置
└── txt/             # 文本数据存储
```

## 快速开始

### 方式一：Docker部署（推荐）

1. 进入Docker部署目录
```bash
cd docker-deploy
```

2. 启动服务
```bash
docker-compose up -d
```

3. 访问应用
- 打开浏览器访问：http://localhost:5000

### 方式二：本地开发运行

#### 后端启动

1. 安装Python依赖
```bash
cd backend
pip install -r requirements.txt
```

2. 配置环境变量（可选）
```bash
# 创建 .env 文件配置OpenAI API
OPENAI_API_KEY=your_api_key
OPENAI_API_URL=https://api.openai.com/v1
```

3. 启动Flask服务
```bash
python app.py
```

#### 前端启动

1. 安装Node.js依赖
```bash
cd frontend
npm install
```

2. 启动开发服务器
```bash
npm run dev
```

3. 访问前端界面
- 打开浏览器访问：http://localhost:3000

## 主要功能

### 数据爬取
- 巨潮资讯网年报数据爬取
- 支持批量股票代码查询
- 自动PDF下载和文本提取
- 重复文件检测和跳过

### 数据分析
- 关键词统计分析
- 关联方分析
- 上下文检索
- Excel数据导出

### AI分析
- OpenAI集成支持
- 智能文本分析
- 流式响应显示
- 分析结果缓存

### 数据管理
- SQLite数据库存储
- 文件导入导出
- 历史记录管理
- 分页显示支持

## 技术栈

### 后端
- Flask 2.3.3
- SQLite数据库
- pandas数据处理
- pdfplumber PDF解析
- OpenAI API集成

### 前端
- React 18
- TypeScript
- Vite构建工具
- Tailwind CSS
- Framer Motion动画

## 配置说明

### 环境变量
```bash
# OpenAI配置（可选）
OPENAI_API_KEY=your_api_key
OPENAI_API_URL=https://api.openai.com/v1

# Flask配置
FLASK_ENV=production
```

### 数据目录
- `pdf/` - PDF文件存储
- `txt/` - 文本文件存储
- `exports/` - 导出文件存储
- `results/` - 分析结果存储

## 部署说明

### Docker部署
使用docker-compose进行一键部署，包含数据持久化和健康检查。

### 手动部署
1. 确保Python 3.8+和Node.js 16+环境
2. 分别启动后端和前端服务
3. 配置反向代理（可选）

## 注意事项

- 需要稳定的网络连接访问巨潮资讯网
- 确保有足够的磁盘空间存储PDF文件
- AI功能需要配置有效的OpenAI API密钥
- 建议使用Docker部署以确保环境一致性
